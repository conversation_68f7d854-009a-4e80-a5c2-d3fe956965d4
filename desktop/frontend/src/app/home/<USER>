<div class="home-container">
  <!-- Empty State -->
  <div *ngIf="tabService.tabsValue.length === 0" class="empty-state">
    <mat-card class="welcome-card">
      <mat-card-header>
        <mat-icon mat-card-avatar>home</mat-icon>
        <mat-card-title>Welcome to NS Drive</mat-card-title>
        <mat-card-subtitle
          >Create your first tab to get started</mat-card-subtitle
        >
      </mat-card-header>
      <mat-card-content>
        <p>
          Tabs allow you to run multiple sync operations simultaneously. Each
          tab can have its own profile and run independently.
        </p>
      </mat-card-content>
      <mat-card-actions>
        <button mat-raised-button color="primary" (click)="createTab()">
          <mat-icon>add</mat-icon>
          Create First Tab
        </button>
      </mat-card-actions>
    </mat-card>
  </div>

  <!-- Tabs Interface -->
  <div *ngIf="tabService.tabsValue.length > 0" class="tabs-container">
    <!-- Vertical Tab Layout -->
    <div class="vertical-tabs-layout">
      <!-- Tab Sidebar -->
      <div class="tab-sidebar">
        <div class="tab-list">
          <div
            *ngFor="let tab of tabService.tabsValue; let i = index"
            class="tab-item"
            [class.active]="getActiveTabIndex() === i"
            (click)="onTabChange(i)"
          >
            <div class="tab-label" *ngIf="!tab.isEditing">
              <span class="tab-name">{{ tab.name }}</span>
              <button
                mat-icon-button
                class="tab-menu-button"
                [matMenuTriggerFor]="tabMenu"
                (click)="$event.stopPropagation()"
              >
                <mat-icon>more_vert</mat-icon>
              </button>

              <mat-menu #tabMenu="matMenu">
                <button mat-menu-item (click)="startRenameTab(tab.id)">
                  <mat-icon>edit</mat-icon>
                  <span>Rename</span>
                </button>
                <button
                  mat-menu-item
                  (click)="deleteTab(tab.id)"
                  [disabled]="tabService.tabsValue.length === 1"
                >
                  <mat-icon>delete</mat-icon>
                  <span>Delete</span>
                </button>
              </mat-menu>
            </div>

            <div class="tab-edit" *ngIf="tab.isEditing">
              <mat-form-field appearance="outline" class="tab-name-field">
                <input
                  matInput
                  #tabNameInput
                  [value]="tab.name"
                  (blur)="finishRenameTab(tab.id, tabNameInput.value)"
                  (keydown.enter)="finishRenameTab(tab.id, tabNameInput.value)"
                  (keydown.escape)="cancelRenameTab(tab.id)"
                  (click)="$event.stopPropagation()"
                  autofocus
                />
              </mat-form-field>
            </div>
          </div>
        </div>

        <!-- Add Tab Button -->
        <button
          mat-fab
          color="primary"
          class="add-tab-button"
          (click)="createTab()"
          matTooltip="Add new tab"
        >
          <mat-icon>add</mat-icon>
        </button>
      </div>

      <!-- Tab Content Area -->
      <div class="tab-content-area">
        <div
          *ngFor="let tab of tabService.tabsValue; let i = index"
          class="tab-content"
          [class.active]="getActiveTabIndex() === i"
        >
          <!-- Control Panel -->
          <mat-card class="control-panel">
            <mat-card-header>
              <mat-card-title>Sync Operations</mat-card-title>
              <mat-card-subtitle>
                Choose a profile and sync operation
              </mat-card-subtitle>
            </mat-card-header>

            <mat-card-content class="mt-5">
              <!-- Profile Selection -->
              <mat-form-field appearance="outline" class="profile-select">
                <mat-label>Select Profile</mat-label>
                <mat-select
                  [value]="tab.selectedProfileIndex"
                  (selectionChange)="changeProfileTab($event.value, tab.id)"
                >
                  <mat-option [value]="null">No profile selected</mat-option>
                  <mat-option
                    *ngFor="
                      let profile of appService.configInfo$.value.profiles;
                      let idx = index
                    "
                    [value]="idx"
                  >
                    {{ profile.name }}
                  </mat-option>
                </mat-select>
              </mat-form-field>

              <!-- Action Buttons -->
              <div *ngIf="validateTabProfileIndex(tab)" class="action-buttons">
                <button
                  mat-raised-button
                  [color]="
                    tab.currentAction === Action.Pull ? 'warn' : 'primary'
                  "
                  [disabled]="
                    (!validateTabProfileIndex(tab) &&
                      tab.currentAction !== Action.Pull) ||
                    tab.isStopping
                  "
                  (click)="
                    tab.currentAction !== Action.Pull
                      ? pullTab(tab.id)
                      : stopCommandTab(tab.id)
                  "
                  matTooltip="Download files from remote"
                >
                  <mat-icon>{{
                    tab.isStopping
                      ? "hourglass_empty"
                      : tab.currentAction === Action.Pull
                      ? "stop"
                      : "download"
                  }}</mat-icon>
                  {{
                    tab.isStopping
                      ? "Stopping..."
                      : tab.currentAction === Action.Pull
                      ? "Stop Pull"
                      : "Pull"
                  }}
                </button>

                <button
                  mat-raised-button
                  [color]="
                    tab.currentAction === Action.Push ? 'warn' : 'accent'
                  "
                  [disabled]="
                    !validateTabProfileIndex(tab) &&
                    tab.currentAction !== Action.Push
                  "
                  (click)="
                    tab.currentAction !== Action.Push
                      ? pushTab(tab.id)
                      : stopCommandTab(tab.id)
                  "
                  matTooltip="Upload files to remote"
                >
                  <mat-icon>{{
                    tab.currentAction === Action.Push ? "stop" : "upload"
                  }}</mat-icon>
                  {{ tab.currentAction === Action.Push ? "Stop Push" : "Push" }}
                </button>

                <button
                  mat-raised-button
                  [color]="tab.currentAction === Action.Bi ? 'warn' : 'primary'"
                  [disabled]="
                    !validateTabProfileIndex(tab) &&
                    tab.currentAction !== Action.Bi
                  "
                  (click)="
                    tab.currentAction !== Action.Bi
                      ? biTab(tab.id)
                      : stopCommandTab(tab.id)
                  "
                  matTooltip="Bidirectional sync"
                >
                  <mat-icon>{{
                    tab.currentAction === Action.Bi ? "stop" : "sync"
                  }}</mat-icon>
                  {{ tab.currentAction === Action.Bi ? "Stop Sync" : "Sync" }}
                </button>

                <button
                  mat-raised-button
                  [color]="
                    tab.currentAction === Action.BiResync ? 'warn' : 'warn'
                  "
                  [disabled]="
                    !validateTabProfileIndex(tab) &&
                    tab.currentAction !== Action.BiResync
                  "
                  (click)="
                    tab.currentAction !== Action.BiResync
                      ? biResyncTab(tab.id)
                      : stopCommandTab(tab.id)
                  "
                  matTooltip="Force resync all files"
                >
                  <mat-icon>{{
                    tab.currentAction === Action.BiResync ? "stop" : "refresh"
                  }}</mat-icon>
                  {{
                    tab.currentAction === Action.BiResync
                      ? "Stop Resync"
                      : "Resync"
                  }}
                </button>
              </div>

              <!-- Progress Indicator -->
              <mat-progress-bar
                *ngIf="tab.currentAction"
                mode="indeterminate"
                class="progress-bar"
              >
              </mat-progress-bar>

              <!-- Status Chip -->
              <mat-chip-set *ngIf="tab.currentAction" class="status-chips">
                <mat-chip [color]="getActionColor(tab.currentAction)" selected>
                  <mat-icon>{{ getActionIcon(tab.currentAction) }}</mat-icon>
                  {{ getActionLabel(tab.currentAction) }}
                </mat-chip>
              </mat-chip-set>
            </mat-card-content>
          </mat-card>

          <!-- Working Directory Info -->
          <mat-card class="info-card">
            <mat-card-header>
              <mat-icon mat-card-avatar>folder</mat-icon>
              <mat-card-title>Working Directory</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <code class="working-dir">{{
                (appService.configInfo$ | async)?.working_dir
              }}</code>
            </mat-card-content>
          </mat-card>

          <!-- Output Console -->
          <mat-card class="output-card">
            <mat-card-header>
              <mat-icon mat-card-avatar>terminal</mat-icon>
              <mat-card-title>Console Output</mat-card-title>
              <div class="spacer"></div>
              <button
                mat-icon-button
                (click)="clearTabOutput(tab.id)"
                matTooltip="Clear output"
              >
                <mat-icon>clear</mat-icon>
              </button>
            </mat-card-header>
            <mat-card-content>
              <div class="console-output">
                <pre>{{ tab.data.join("\n") || "No output yet..." }}</pre>
              </div>
            </mat-card-content>
          </mat-card>
        </div>
      </div>
    </div>
  </div>
</div>
