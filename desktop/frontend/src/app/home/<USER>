@use "@angular/material" as mat;

// Light border styling for Material components
::ng-deep .mat-mdc-form-field-outline {
  .mdc-notched-outline__leading,
  .mdc-notched-outline__notch,
  .mdc-notched-outline__trailing {
    border-color: #e0e0e0 !important;
  }
}

::ng-deep
  .mat-mdc-form-field-outline:not(.mdc-notched-outline--upgraded)
  .mdc-notched-outline__outline {
  border-color: #e0e0e0 !important;
}

::ng-deep .mat-mdc-form-field-focus-overlay {
  background-color: transparent !important;
}

::ng-deep .mat-mdc-form-field.mat-focused .mat-mdc-form-field-outline {
  .mdc-notched-outline__leading,
  .mdc-notched-outline__notch,
  .mdc-notched-outline__trailing {
    border-color: #e0e0e0 !important;
  }
}

::ng-deep .mat-mdc-select-panel {
  border: 1px solid #e0e0e0 !important;
}

::ng-deep .mat-mdc-select-trigger {
  border-color: #e0e0e0 !important;
}

.home-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

// Empty State
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 24px;

  .welcome-card {
    max-width: 500px;
    text-align: center;

    mat-card-header {
      justify-content: center;

      mat-icon {
        font-size: 48px;
        width: 48px;
        height: 48px;
        color: #424242;
      }
    }

    mat-card-content {
      padding: 16px 24px;

      p {
        color: #666;
        line-height: 1.6;
        margin: 0;
      }
    }

    mat-card-actions {
      justify-content: center;
      padding: 16px 24px 24px;
    }
  }
}

// Tabs Container
.tabs-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

// Modern Tabs Layout
.modern-tabs-layout {
  display: flex;
  flex-direction: column;
  height: 100%;
  flex: 1;
  background: #f8f9fa;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.tab-navigation {
  background: linear-gradient(135deg, #f1f3f4 0%, #e8eaed 100%);
  border-bottom: 1px solid #dadce0;
  flex-shrink: 0;
  position: relative;
}

.tab-nav-container {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  gap: 16px;
  min-height: 64px;
}

.tab-list-wrapper {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.tab-list {
  display: flex;
  gap: 4px;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  padding: 4px 0;

  &::-webkit-scrollbar {
    display: none;
  }
}

.tab-item {
  display: flex;
  align-items: center;
  background: #ffffff;
  border: 1px solid #dadce0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  min-width: 160px;
  max-width: 240px;
  flex-shrink: 0;
  position: relative;
  overflow: hidden;

  &:hover {
    background: #f8f9fa;
    border-color: #bdc1c6;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  &.active {
    background: #5f6368;
    border-color: #5f6368;
    color: #ffffff;
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(95, 99, 104, 0.3);

    .tab-icon {
      color: #ffffff;
    }

    .tab-name {
      color: #ffffff;
      font-weight: 600;
    }

    .tab-menu-button {
      color: #ffffff;
    }
  }
}

.tab-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 12px 16px;
}

.tab-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
}

.tab-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
  color: #5f6368;
  flex-shrink: 0;
}

.tab-name {
  font-size: 14px;
  font-weight: 500;
  color: #202124;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  letter-spacing: 0.25px;
}

.tab-actions {
  display: flex;
  align-items: center;
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
}

.tab-item:hover .tab-actions {
  opacity: 1;
}

.tab-menu-button {
  width: 24px;
  height: 24px;
  color: #5f6368;

  mat-icon {
    font-size: 16px;
    width: 16px;
    height: 16px;
  }
}

.add-tab-container {
  flex-shrink: 0;
}

.add-tab-button {
  width: 40px;
  height: 40px;
  background: #ffffff;
  border: 2px dashed #bdc1c6;
  color: #5f6368;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    background: #f8f9fa;
    border-color: #5f6368;
    color: #202124;
    transform: scale(1.05);
  }

  mat-icon {
    font-size: 20px;
    width: 20px;
    height: 20px;
  }
}

.tab-content-area {
  flex: 1;
  overflow: hidden;
  position: relative;
  background: #ffffff;
}

// Tab Label Styling
.tab-label {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  flex: 1;
  height: 24px;

  .tab-name {
    flex: 1;
    text-align: left;
    font-size: 14px;
    line-height: 1.4;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .tab-menu-button {
    opacity: 0;
    transition: opacity 0.2s;

    mat-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
      margin: 0;
      padding: 0;
    }
  }
}

.tab-item:hover .tab-label .tab-menu-button {
  opacity: 1;
}

.tab-edit {
  display: flex;
  align-items: center;
  width: 100%;

  .tab-name-field {
    width: 100%;

    ::ng-deep .mat-mdc-form-field-wrapper {
      padding-bottom: 0;
    }

    ::ng-deep .mat-mdc-text-field-wrapper {
      height: 32px;
      background: white;
      border-radius: 4px;
    }

    ::ng-deep .mat-mdc-form-field-subscript-wrapper {
      display: none;
    }

    input {
      font-size: 14px;
      padding: 4px 8px;
    }
  }
}

// Tab Content
.tab-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s ease-in-out;
  padding: 24px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 24px;

  &.active {
    opacity: 1;
    visibility: visible;
  }
}

// Control Panel
.control-panel {
  .profile-select {
    width: 100%;
    margin-bottom: 16px;
  }

  .action-buttons {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 16px;

    button {
      width: 100%;
      height: 40px;
      font-weight: 500;
      border-radius: 8px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      &:not(:disabled) {
        &:hover {
          transform: translateY(-1px);
        }

        &:active {
          transform: translateY(0);
        }
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        box-shadow: none;
      }

      mat-icon {
        margin-right: 8px;
      }
    }
  }

  .progress-bar {
    margin: 16px 0;
  }

  .status-chips {
    margin-top: 8px;

    mat-chip {
      mat-icon {
        margin-right: 4px;
        font-size: 16px;
        width: 16px;
        height: 16px;
      }
    }
  }
}

// Info Card
.info-card {
  .working-dir {
    background: #f5f5f5;
    padding: 12px;
    border-radius: 4px;
    display: block;
    word-break: break-all;
    font-family: "Courier New", monospace;
    font-size: 14px;
    color: #333;
  }
}

// Output Card
.output-card {
  flex: 1;
  display: flex;
  flex-direction: column;

  mat-card-header {
    .spacer {
      flex: 1;
    }
  }

  mat-card-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 0 !important;
  }

  .console-output {
    flex: 1;
    background: #1e1e1e;
    color: #d4d4d4;
    padding: 16px;
    border-radius: 4px;
    overflow: auto;
    font-family: "Courier New", monospace;
    font-size: 13px;
    line-height: 1.4;
    min-height: 200px;

    pre {
      margin: 0;
      white-space: pre-wrap;
      word-wrap: break-word;
    }
  }
}

// Responsive Design
.vertical-tabs-layout {
  flex-direction: column;
}

.tab-sidebar {
  width: 100%;
  min-width: auto;
  border-right: none;
  border-bottom: 1px solid #e0e0e0;
  padding: 8px 0;
}

.tab-list {
  flex-direction: row;
  overflow-x: auto;
  padding: 0 8px;
  gap: 8px;
}

.tab-item {
  flex-shrink: 0;
  min-width: 120px;
}

.add-tab-button {
  margin: 8px 16px;
  width: 32px;
  height: 32px;
  flex-shrink: 0;
}

.tab-content {
  padding: 16px;
  gap: 16px;
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  // Global text colors for dark mode
  .home-container {
    background-color: #1e1e1e;
    color: #e8eaed;
  }

  .empty-state .welcome-card {
    mat-card-header mat-icon {
      color: #e0e0e0;
    }

    mat-card-content p {
      color: #e0e0e0;
    }

    mat-card-title {
      color: #ffffff;
    }

    mat-card-subtitle {
      color: #bdbdbd;
    }
  }

  .info-card {
    .working-dir {
      background: #2d2d2d;
      color: #e0e0e0;
    }

    mat-card-title {
      color: #ffffff;
    }

    mat-card-subtitle {
      color: #bdbdbd;
    }
  }

  .console-output {
    background: #0d1117;
    color: #c9d1d9;
  }

  .modern-tabs-layout {
    background: #2d2d2d;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.3);
  }

  .tab-navigation {
    background: linear-gradient(135deg, #2d2d2d 0%, #1e1e1e 100%);
    border-bottom-color: #424242;
  }

  .tab-item {
    background: #1e1e1e;
    border-color: #424242;
    color: #e0e0e0;

    &:hover {
      background: #2d2d2d;
      border-color: #5f6368;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }

    &.active {
      background: #bdc1c6;
      border-color: #bdc1c6;
      color: #202124;
      box-shadow: 0 6px 16px rgba(189, 193, 198, 0.3);

      .tab-icon {
        color: #202124;
      }

      .tab-name {
        color: #202124;
      }

      .tab-menu-button {
        color: #202124;
      }
    }
  }

  .tab-icon {
    color: #bdbdbd;
  }

  .tab-name {
    color: #e0e0e0;
  }

  .tab-menu-button {
    color: #bdbdbd;
  }

  .add-tab-button {
    background: #1e1e1e;
    border-color: #424242;
    color: #bdbdbd;

    &:hover {
      background: #2d2d2d;
      border-color: #bdc1c6;
      color: #e0e0e0;
    }
  }

  .tab-content-area {
    background: #1e1e1e;
  }

  .tab-edit .tab-name-field {
    ::ng-deep .mat-mdc-text-field-wrapper {
      background: #2d2d2d;
    }

    input {
      color: #e0e0e0;
    }
  }

  .control-panel {
    mat-card-title {
      color: #ffffff;
    }

    mat-card-subtitle {
      color: #bdbdbd;
    }

    .status-chips mat-chip {
      color: #e0e0e0;
    }

    // Light text styling for profile select in dark mode
    .profile-select {
      ::ng-deep .mat-mdc-select-value {
        color: #e0e0e0 !important;
      }

      ::ng-deep .mat-mdc-select-placeholder {
        color: #bdbdbd !important;
      }

      ::ng-deep .mat-mdc-form-field-label {
        color: #bdbdbd !important;
      }

      ::ng-deep .mat-mdc-select-arrow {
        color: #e0e0e0 !important;
      }

      // Light text for dropdown options
      ::ng-deep .mat-mdc-option {
        color: #e0e0e0 !important;
      }
    }
  }

  .output-card {
    mat-card-title {
      color: #ffffff;
    }

    mat-card-subtitle {
      color: #bdbdbd;
    }
  }

  // Dark theme responsive design
  @media (max-width: 768px) {
    .tab-sidebar {
      background: #1e1e1e;
      border-bottom-color: #424242;
    }
  }
}
