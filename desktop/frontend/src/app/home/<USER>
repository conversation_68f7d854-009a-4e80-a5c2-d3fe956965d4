@use "@angular/material" as mat;

// Light border styling for Material components
::ng-deep .mat-mdc-form-field-outline {
  .mdc-notched-outline__leading,
  .mdc-notched-outline__notch,
  .mdc-notched-outline__trailing {
    border-color: #e0e0e0 !important;
  }
}

::ng-deep
  .mat-mdc-form-field-outline:not(.mdc-notched-outline--upgraded)
  .mdc-notched-outline__outline {
  border-color: #e0e0e0 !important;
}

::ng-deep .mat-mdc-form-field-focus-overlay {
  background-color: transparent !important;
}

::ng-deep .mat-mdc-form-field.mat-focused .mat-mdc-form-field-outline {
  .mdc-notched-outline__leading,
  .mdc-notched-outline__notch,
  .mdc-notched-outline__trailing {
    border-color: #e0e0e0 !important;
  }
}

::ng-deep .mat-mdc-select-panel {
  border: 1px solid #e0e0e0 !important;
}

::ng-deep .mat-mdc-select-trigger {
  border-color: #e0e0e0 !important;
}

.home-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

// Empty State
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 24px;

  .welcome-card {
    max-width: 500px;
    text-align: center;

    mat-card-header {
      justify-content: center;

      mat-icon {
        font-size: 48px;
        width: 48px;
        height: 48px;
        color: #424242;
      }
    }

    mat-card-content {
      padding: 16px 24px;

      p {
        color: #666;
        line-height: 1.6;
        margin: 0;
      }
    }

    mat-card-actions {
      justify-content: center;
      padding: 16px 24px 24px;
    }
  }
}

// Tabs Container
.tabs-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

// Vertical Tabs Layout
.vertical-tabs-layout {
  display: flex;
  flex-direction: column;
  height: 100%;
  flex: 1;
}

.tab-header {
  background: #fafafa;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
  padding: 16px 24px;
  gap: 16px;
  min-height: 80px;
  flex-shrink: 0;
}

.tab-list {
  display: flex;
  flex-direction: row;
  gap: 8px;
  flex: 1;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;

  &::-webkit-scrollbar {
    display: none;
  }
}

.tab-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.2s ease-in-out;
  min-height: 48px;
  border: 1px solid transparent;
  white-space: nowrap;
  flex-shrink: 0;
  min-width: 120px;

  &:hover {
    background: rgba(0, 0, 0, 0.04);
  }

  &.active {
    background: #424242;
    border-color: transparent;
    color: #ffffff;

    .tab-name {
      font-weight: 600;
    }
  }
}

.tab-content-area {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.add-tab-button {
  width: 40px;
  height: 40px;
  flex-shrink: 0;

  mat-icon {
    font-size: 20px;
  }
}

// Tab Label Styling
.tab-label {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  flex: 1;
  height: 24px;

  .tab-name {
    flex: 1;
    text-align: left;
    font-size: 14px;
    line-height: 1.4;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .tab-menu-button {
    opacity: 0;
    transition: opacity 0.2s;

    mat-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
      margin: 0;
      padding: 0;
    }
  }
}

.tab-item:hover .tab-label .tab-menu-button {
  opacity: 1;
}

.tab-edit {
  display: flex;
  align-items: center;
  width: 100%;

  .tab-name-field {
    width: 100%;

    ::ng-deep .mat-mdc-form-field-wrapper {
      padding-bottom: 0;
    }

    ::ng-deep .mat-mdc-text-field-wrapper {
      height: 32px;
      background: white;
      border-radius: 4px;
    }

    ::ng-deep .mat-mdc-form-field-subscript-wrapper {
      display: none;
    }

    input {
      font-size: 14px;
      padding: 4px 8px;
    }
  }
}

// Tab Content
.tab-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s ease-in-out;
  padding: 24px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 24px;

  &.active {
    opacity: 1;
    visibility: visible;
  }
}

// Control Panel
.control-panel {
  .profile-select {
    width: 100%;
    margin-bottom: 16px;
  }

  .action-buttons {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 16px;

    button {
      width: 100%;
      height: 40px;
      font-weight: 500;
      border-radius: 8px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      &:not(:disabled) {
        &:hover {
          transform: translateY(-1px);
        }

        &:active {
          transform: translateY(0);
        }
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        box-shadow: none;
      }

      mat-icon {
        margin-right: 8px;
      }
    }
  }

  .progress-bar {
    margin: 16px 0;
  }

  .status-chips {
    margin-top: 8px;

    mat-chip {
      mat-icon {
        margin-right: 4px;
        font-size: 16px;
        width: 16px;
        height: 16px;
      }
    }
  }
}

// Info Card
.info-card {
  .working-dir {
    background: #f5f5f5;
    padding: 12px;
    border-radius: 4px;
    display: block;
    word-break: break-all;
    font-family: "Courier New", monospace;
    font-size: 14px;
    color: #333;
  }
}

// Output Card
.output-card {
  flex: 1;
  display: flex;
  flex-direction: column;

  mat-card-header {
    .spacer {
      flex: 1;
    }
  }

  mat-card-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 0 !important;
  }

  .console-output {
    flex: 1;
    background: #1e1e1e;
    color: #d4d4d4;
    padding: 16px;
    border-radius: 4px;
    overflow: auto;
    font-family: "Courier New", monospace;
    font-size: 13px;
    line-height: 1.4;
    min-height: 200px;

    pre {
      margin: 0;
      white-space: pre-wrap;
      word-wrap: break-word;
    }
  }
}

// Responsive Design
.vertical-tabs-layout {
  flex-direction: column;
}

.tab-sidebar {
  width: 100%;
  min-width: auto;
  border-right: none;
  border-bottom: 1px solid #e0e0e0;
  padding: 8px 0;
}

.tab-list {
  flex-direction: row;
  overflow-x: auto;
  padding: 0 8px;
  gap: 8px;
}

.tab-item {
  flex-shrink: 0;
  min-width: 120px;
}

.add-tab-button {
  margin: 8px 16px;
  width: 32px;
  height: 32px;
  flex-shrink: 0;
}

.tab-content {
  padding: 16px;
  gap: 16px;
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  // Global text colors for dark mode
  .home-container {
    color: #e0e0e0;
  }

  .empty-state .welcome-card {
    mat-card-header mat-icon {
      color: #e0e0e0;
    }

    mat-card-content p {
      color: #e0e0e0;
    }

    mat-card-title {
      color: #ffffff;
    }

    mat-card-subtitle {
      color: #bdbdbd;
    }
  }

  .info-card {
    .working-dir {
      background: #2d2d2d;
      color: #e0e0e0;
    }

    mat-card-title {
      color: #ffffff;
    }

    mat-card-subtitle {
      color: #bdbdbd;
    }
  }

  .console-output {
    background: #0d1117;
    color: #c9d1d9;
  }

  .tab-header {
    background: #1e1e1e;
    border-bottom-color: #424242;
  }

  .tab-item {
    color: #e0e0e0;

    &:hover {
      background: rgba(255, 255, 255, 0.08);
    }

    &.active {
      background: #1a237e;
      border-color: #3f51b5;
      color: #ffffff;
    }
  }

  .tab-edit .tab-name-field {
    ::ng-deep .mat-mdc-text-field-wrapper {
      background: #2d2d2d;
    }

    input {
      color: #e0e0e0;
    }
  }

  .control-panel {
    mat-card-title {
      color: #ffffff;
    }

    mat-card-subtitle {
      color: #bdbdbd;
    }

    .status-chips mat-chip {
      color: #e0e0e0;
    }

    // Light text styling for profile select in dark mode
    .profile-select {
      ::ng-deep .mat-mdc-select-value {
        color: #e0e0e0 !important;
      }

      ::ng-deep .mat-mdc-select-placeholder {
        color: #bdbdbd !important;
      }

      ::ng-deep .mat-mdc-form-field-label {
        color: #bdbdbd !important;
      }

      ::ng-deep .mat-mdc-select-arrow {
        color: #e0e0e0 !important;
      }

      // Light text for dropdown options
      ::ng-deep .mat-mdc-option {
        color: #e0e0e0 !important;
      }
    }
  }

  .output-card {
    mat-card-title {
      color: #ffffff;
    }

    mat-card-subtitle {
      color: #bdbdbd;
    }
  }

  // Dark theme responsive design
  @media (max-width: 768px) {
    .tab-sidebar {
      background: #1e1e1e;
      border-bottom-color: #424242;
    }
  }
}
