<div class="profiles-container">
  <!-- Header Section -->
  <mat-card class="header-card">
    <mat-card-header>
      <mat-icon mat-card-avatar>settings</mat-icon>
      <mat-card-title>Profile Management</mat-card-title>
      <mat-card-subtitle>
        Configure sync profiles for different directories and remotes
      </mat-card-subtitle>
    </mat-card-header>

    <mat-card-actions>
      <button mat-raised-button color="primary" (click)="addProfile()">
        <mat-icon>add</mat-icon>
        Add Profile
      </button>
      <div class="spacer"></div>
      <button mat-raised-button color="accent" (click)="saveConfigInfo()">
        <mat-icon>save</mat-icon>
        {{ saveBtnText$ | async }}
      </button>
    </mat-card-actions>
  </mat-card>

  <!-- Profiles List -->
  <div class="profiles-list">
    <mat-accordion
      *ngIf="
        (appService.configInfo$ | async)?.profiles?.length;
        else noProfiles
      "
    >
      <mat-expansion-panel
        *ngFor="
          let setting of (appService.configInfo$ | async)?.profiles;
          let idx = index
        "
        class="profile-panel"
      >
        <mat-expansion-panel-header>
          <mat-panel-title>
            <mat-icon>folder</mat-icon>
            <div>
              <span>{{ setting.name || "Untitled Profile" }}</span> <br />
              <span class="font-normal italic text-xs">
                {{ getProfileDescription(setting) }}
              </span>
            </div>
          </mat-panel-title>
        </mat-expansion-panel-header>

        <!-- Profile Form -->
        <div class="profile-form">
          <!-- Basic Information -->
          <mat-card class="form-section">
            <mat-card-header>
              <mat-card-title>Basic Information</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Profile Name</mat-label>
                <input
                  matInput
                  [(ngModel)]="setting.name"
                  placeholder="Enter profile name"
                />
                <mat-icon matSuffix>edit</mat-icon>
              </mat-form-field>
            </mat-card-content>
          </mat-card>

          <!-- Path Configuration -->
          <mat-card class="form-section">
            <mat-card-header>
              <mat-card-title>Path Configuration</mat-card-title>
              <mat-card-subtitle>
                Configure source and destination paths
              </mat-card-subtitle>
            </mat-card-header>
            <mat-card-content>
              <!-- From Path -->
              <div class="path-section">
                <h4>Source Path</h4>
                <div class="path-input-group">
                  <mat-form-field appearance="outline" class="remote-select">
                    <mat-label>Remote</mat-label>
                    <mat-select
                      [value]="getFromRemote(setting)"
                      (selectionChange)="
                        updateFromPath(
                          setting,
                          $event.value,
                          getFromPath(setting)
                        )
                      "
                    >
                      <mat-option value="">Local</mat-option>
                      <mat-option
                        *ngFor="let remote of appService.remotes$ | async"
                        [value]="remote.name"
                      >
                        {{ remote.name }}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>

                  <mat-form-field appearance="outline" class="path-input">
                    <mat-label>Path</mat-label>
                    <input
                      matInput
                      [value]="getFromPath(setting)"
                      (input)="
                        updateFromPath(
                          setting,
                          getFromRemote(setting),
                          $any($event.target).value
                        )
                      "
                      placeholder="/source/path"
                    />
                    <mat-icon matSuffix>folder_open</mat-icon>
                  </mat-form-field>
                </div>
              </div>

              <!-- To Path -->
              <div class="path-section">
                <h4>Destination Path</h4>
                <div class="path-input-group">
                  <mat-form-field appearance="outline" class="remote-select">
                    <mat-label>Remote</mat-label>
                    <mat-select
                      [value]="getToRemote(setting)"
                      (selectionChange)="
                        updateToPath(setting, $event.value, getToPath(setting))
                      "
                    >
                      <mat-option value="">Local</mat-option>
                      <mat-option
                        *ngFor="let remote of appService.remotes$ | async"
                        [value]="remote.name"
                      >
                        {{ remote.name }}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>

                  <mat-form-field appearance="outline" class="path-input">
                    <mat-label>Path</mat-label>
                    <input
                      matInput
                      [value]="getToPath(setting)"
                      (input)="
                        updateToPath(
                          setting,
                          getToRemote(setting),
                          $any($event.target).value
                        )
                      "
                      placeholder="/destination/path"
                    />
                    <mat-icon matSuffix>folder_open</mat-icon>
                  </mat-form-field>
                </div>
              </div>
            </mat-card-content>
          </mat-card>

          <!-- Performance Settings -->
          <mat-card class="form-section">
            <mat-card-header>
              <mat-card-title>Performance Settings</mat-card-title>
              <mat-card-subtitle>
                Configure parallel transfers and bandwidth limits
              </mat-card-subtitle>
            </mat-card-header>
            <mat-card-content>
              <div class="performance-grid mt-5">
                <mat-form-field appearance="outline">
                  <mat-label>Parallel Transfers</mat-label>
                  <mat-select [(ngModel)]="setting.parallel">
                    <mat-option
                      *ngFor="let num of getNumberRange(1, 32)"
                      [value]="num"
                    >
                      {{ num }}
                    </mat-option>
                  </mat-select>
                  <mat-icon matSuffix>speed</mat-icon>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Bandwidth Limit (MB/s)</mat-label>
                  <mat-select [(ngModel)]="setting.bandwidth">
                    <mat-option
                      *ngFor="let num of getNumberRange(1, 100)"
                      [value]="num"
                    >
                      {{ num }}
                    </mat-option>
                  </mat-select>
                  <mat-icon matSuffix>network_check</mat-icon>
                </mat-form-field>
              </div>
            </mat-card-content>
          </mat-card>

          <!-- Actions -->
          <mat-card class="form-section actions-section">
            <mat-card-actions>
              <button
                mat-raised-button
                color="warn"
                (click)="removeProfile(idx)"
              >
                <mat-icon>delete</mat-icon>
                Delete Profile
              </button>
            </mat-card-actions>
          </mat-card>
        </div>
      </mat-expansion-panel>
    </mat-accordion>

    <!-- No Profiles State -->
    <ng-template #noProfiles>
      <mat-card class="no-profiles-card">
        <mat-card-header>
          <mat-icon mat-card-avatar>folder_off</mat-icon>
          <mat-card-title>No Profiles Found</mat-card-title>
          <mat-card-subtitle
            >Create your first sync profile to get started</mat-card-subtitle
          >
        </mat-card-header>
        <mat-card-content>
          <p>
            Profiles allow you to configure different sync settings for various
            directories and remotes.
          </p>
        </mat-card-content>
        <mat-card-actions>
          <button mat-raised-button color="primary" (click)="addProfile()">
            <mat-icon>add</mat-icon>
            Create First Profile
          </button>
        </mat-card-actions>
      </mat-card>
    </ng-template>
  </div>
</div>
