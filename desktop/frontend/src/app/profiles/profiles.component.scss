.profiles-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 24px;
  overflow-y: auto;
}

// Header Card
.header-card {
  mat-card-actions {
    display: flex;
    align-items: center;
    gap: 16px;

    .spacer {
      flex: 1;
    }
  }
}

// Profiles List
.profiles-list {
  flex: 1;
}

// Profile Panel
.profile-panel {
  margin-bottom: 16px;
  background-color: #2d2d2d !important;
  color: #e0e0e0;

  mat-expansion-panel-header {
    background-color: #2d2d2d !important;
    color: #e0e0e0;

    mat-panel-title {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #ffffff;

      mat-icon {
        color: #e0e0e0;
      }
    }

    mat-panel-description {
      color: #bdbdbd;
    }
  }

  .mat-expansion-panel-body {
    background-color: #2d2d2d !important;
    color: #e0e0e0;
  }
}

// Profile Form
.profile-form {
  padding: 16px 0;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-section {
  background-color: #3d3d3d !important;
  color: #e0e0e0;

  mat-card-header {
    mat-card-title {
      color: #ffffff;
    }

    mat-card-subtitle {
      color: #bdbdbd;
    }
  }

  mat-card-content {
    color: #e0e0e0;
  }

  &.actions-section {
    mat-card-actions {
      justify-content: flex-end;
    }
  }
}

.full-width {
  width: 100%;
}

.path-section {
  h4 {
    margin: 0 0 16px 0;
    color: #e0e0e0;
    font-size: 14px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
}

.path-input-group {
  display: flex;
  gap: 12px;
  align-items: flex-start;

  .remote-select {
    min-width: 120px;
    flex-shrink: 0;
  }

  .path-input {
    flex: 1;
  }
}

// Performance Settings
.performance-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }

  // Fix for select fields showing unnecessary prefix space
  .mat-mdc-form-field {
    .mat-mdc-form-field-prefix {
      display: none;
    }

    .mat-mdc-form-field-infix {
      padding-left: 0 !important;
    }
  }
}

// No Profiles State
.no-profiles-card {
  text-align: center;
  max-width: 500px;
  margin: 0 auto;

  mat-card-header {
    justify-content: center;

    mat-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      color: #999;
    }
  }

  mat-card-content {
    padding: 16px 24px;

    p {
      color: #666;
      line-height: 1.6;
      margin: 0;
    }
  }

  mat-card-actions {
    justify-content: center;
    padding: 16px 24px 24px;
  }
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  .profiles-container {
    color: #e0e0e0;
  }

  .header-card {
    mat-card-title {
      color: #ffffff;
    }

    mat-card-subtitle {
      color: #bdbdbd;
    }
  }

  .no-profiles-card {
    mat-card-header {
      mat-card-title {
        color: #ffffff;
      }

      mat-card-subtitle {
        color: #bdbdbd;
      }

      mat-icon {
        color: #757575;
      }
    }

    mat-card-content p {
      color: #e0e0e0;
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .profiles-container {
    padding: 16px;
    gap: 16px;
  }

  .path-input-group {
    flex-direction: column;

    .remote-select {
      min-width: unset;
    }
  }
}
