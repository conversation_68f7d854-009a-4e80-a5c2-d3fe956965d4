.profiles-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 24px;
  overflow-y: auto;
  background-color: #f5f5f5;
}

// Header Card
.header-card {
  background: linear-gradient(135deg, #fafafa 0%, #f1f3f4 100%);
  border-radius: 16px !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
  border: 1px solid #dadce0;
  overflow: hidden;

  mat-card-header {
    padding: 32px 32px 24px 32px;
    background: transparent;

    .mat-mdc-card-avatar {
      background: linear-gradient(135deg, #5f6368, #80868b);
      color: #ffffff;
      width: 56px;
      height: 56px;
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: center;

      mat-icon {
        font-size: 28px;
        width: 28px;
        height: 28px;
        color: #ffffff;
      }
    }

    mat-card-title {
      font-size: 24px;
      font-weight: 600;
      color: #212121;
      margin-bottom: 8px;
      letter-spacing: -0.5px;
    }

    mat-card-subtitle {
      font-size: 14px;
      color: #5f6368;
      line-height: 1.5;
      font-weight: 400;
    }
  }

  mat-card-actions {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 0 32px 32px 32px;
    background: transparent;

    .spacer {
      flex: 1;
    }

    button {
      border-radius: 12px !important;
      font-weight: 500;
      padding: 0 24px;
      height: 44px;
      text-transform: none;
      letter-spacing: 0.25px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
      }

      mat-icon {
        margin-right: 8px;
        font-size: 18px;
        width: 18px;
        height: 18px;
      }
    }
  }
}

// Profiles List
.profiles-list {
  flex: 1;
}

// Profile Panel
.profile-panel {
  margin-bottom: 20px;
  border-radius: 12px !important;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  background-color: #ffffff !important;
  border: 1px solid #e0e0e0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15) !important;
    transform: translateY(-2px);
  }

  mat-expansion-panel-header {
    background-color: #ffffff !important;
    color: #212121;
    padding: 20px 24px !important;
    min-height: 80px !important;
    border-bottom: 1px solid #f5f5f5;

    &:hover {
      background-color: #fafafa !important;
    }

    mat-panel-title {
      display: flex;
      align-items: flex-start;
      gap: 16px;
      color: #212121;
      width: 100%;

      mat-icon {
        color: #424242;
        font-size: 24px;
        width: 24px;
        height: 24px;
        margin-top: 2px;
        flex-shrink: 0;
      }

      div {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 4px;

        span:first-child {
          font-size: 16px;
          font-weight: 600;
          color: #212121;
          line-height: 1.4;
        }

        span:last-child {
          font-size: 12px;
          font-weight: 400;
          color: #757575;
          line-height: 1.3;
          font-style: italic;
        }
      }
    }

    mat-panel-description {
      color: #757575;
    }
  }

  .mat-expansion-panel-body {
    background-color: #fafafa !important;
    color: #212121;
    padding: 0 !important;
  }
}

// Profile Form
.profile-form {
  padding: 16px 0;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-section {
  background-color: #3d3d3d !important;
  color: #e0e0e0;

  mat-card-header {
    mat-card-title {
      color: #ffffff;
    }

    mat-card-subtitle {
      color: #bdbdbd;
    }
  }

  mat-card-content {
    color: #e0e0e0;
  }

  &.actions-section {
    mat-card-actions {
      justify-content: flex-end;
    }
  }
}

.full-width {
  width: 100%;
}

.path-section {
  h4 {
    margin: 0 0 16px 0;
    color: #e0e0e0;
    font-size: 14px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
}

.path-input-group {
  display: flex;
  gap: 12px;
  align-items: flex-start;

  .remote-select {
    min-width: 120px;
    flex-shrink: 0;
  }

  .path-input {
    flex: 1;
  }
}

// Performance Settings
.performance-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }

  // Fix for select fields showing unnecessary prefix space
  .mat-mdc-form-field {
    .mat-mdc-form-field-prefix {
      display: none;
    }

    .mat-mdc-form-field-infix {
      padding-left: 0 !important;
    }
  }
}

// No Profiles State
.no-profiles-card {
  text-align: center;
  max-width: 500px;
  margin: 0 auto;

  mat-card-header {
    justify-content: center;

    mat-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      color: #999;
    }
  }

  mat-card-content {
    padding: 16px 24px;

    p {
      color: #666;
      line-height: 1.6;
      margin: 0;
    }
  }

  mat-card-actions {
    justify-content: center;
    padding: 16px 24px 24px;
  }
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  .profiles-container {
    background-color: #1e1e1e;
    color: #e8eaed;
  }

  .header-card {
    background: linear-gradient(135deg, #1e1e1e 0%, #2d2d2d 100%);
    border-color: #424242;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;

    mat-card-header {
      .mat-mdc-card-avatar {
        background: linear-gradient(135deg, #e0e0e0, #bdbdbd);
        color: #121212;

        mat-icon {
          color: #121212;
        }
      }

      mat-card-title {
        color: #ffffff;
      }

      mat-card-subtitle {
        color: #bdbdbd;
      }
    }

    mat-card-actions {
      button {
        &:hover {
          box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
        }
      }
    }
  }

  // Profile Panel Dark Mode
  .profile-panel {
    background-color: #1e1e1e !important;
    border-color: #424242;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;

    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4) !important;
    }

    mat-expansion-panel-header {
      background-color: #1e1e1e !important;
      color: #e0e0e0;
      border-bottom-color: #2d2d2d;

      &:hover {
        background-color: #2d2d2d !important;
      }

      mat-panel-title {
        color: #e0e0e0;

        mat-icon {
          color: #bdbdbd;
        }

        div {
          span:first-child {
            color: #ffffff;
          }

          span:last-child {
            color: #bdbdbd;
          }
        }
      }

      mat-panel-description {
        color: #bdbdbd;
      }
    }

    .mat-expansion-panel-body {
      background-color: #121212 !important;
      color: #e0e0e0;
    }
  }

  .no-profiles-card {
    mat-card-header {
      mat-card-title {
        color: #ffffff;
      }

      mat-card-subtitle {
        color: #bdbdbd;
      }

      mat-icon {
        color: #757575;
      }
    }

    mat-card-content p {
      color: #e0e0e0;
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .profiles-container {
    padding: 16px;
    gap: 16px;
  }

  .path-input-group {
    flex-direction: column;

    .remote-select {
      min-width: unset;
    }
  }
}
