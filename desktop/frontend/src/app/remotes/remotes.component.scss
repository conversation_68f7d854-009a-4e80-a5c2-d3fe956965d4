summary {
  @apply select-none;
}

// Icon styling - lighter colors
mat-icon {
  color: #9e9e9e !important; // Light gray for light theme
}

.remotes-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 24px;
  overflow-y: auto;
  background-color: #f5f5f5;
}

// Header Card
.header-card {
  background: linear-gradient(135deg, #fafafa 0%, #f1f3f4 100%);
  border-radius: 16px !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
  border: 1px solid #dadce0;
  overflow: hidden;

  mat-card-header {
    padding: 32px 32px 24px 32px;
    background: transparent;

    .mat-mdc-card-avatar {
      background: linear-gradient(135deg, #1976d2, #42a5f5);
      color: #ffffff;
      width: 56px;
      height: 56px;
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: center;

      mat-icon {
        font-size: 28px;
        width: 28px;
        height: 28px;
        color: #ffffff;
      }
    }

    mat-card-title {
      font-size: 24px;
      font-weight: 600;
      color: #212121;
      margin-bottom: 8px;
      letter-spacing: -0.5px;
    }

    mat-card-subtitle {
      font-size: 14px;
      color: #5f6368;
      line-height: 1.5;
      font-weight: 400;
    }
  }

  mat-card-actions {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 0 32px 32px 32px;
    background: transparent;

    button {
      border-radius: 12px !important;
      font-weight: 500;
      padding: 0 24px;
      height: 44px;
      text-transform: none;
      letter-spacing: 0.25px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
      }

      mat-icon {
        margin-right: 8px;
        font-size: 18px;
        width: 18px;
        height: 18px;
      }
    }
  }
}

// Remotes List
.remotes-list {
  flex: 1;
}

// Remote Item
.remote-item {
  border-bottom: 1px solid #e0e0e0;

  &:last-child {
    border-bottom: none;
  }

  .remote-actions {
    display: flex;
    gap: 8px;
  }
}

// No Remotes State
.no-remotes-card {
  text-align: center;
  max-width: 500px;
  margin: 0 auto;

  mat-card-header {
    justify-content: center;

    mat-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      color: #999;
    }
  }

  mat-card-content {
    padding: 16px 24px;

    p {
      color: #666;
      line-height: 1.6;
      margin: 0;
    }
  }

  mat-card-actions {
    justify-content: center;
    padding: 16px 24px 24px;
  }
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  // Light icon colors for dark theme
  mat-icon {
    color: #bdbdbd !important; // Lighter gray for dark theme
  }

  // Header Card Dark Mode
  .header-card {
    background: linear-gradient(135deg, #1e1e1e 0%, #2d2d2d 100%);
    border-color: #424242;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;

    mat-card-header {
      .mat-mdc-card-avatar {
        background: linear-gradient(135deg, #64b5f6, #90caf9);
        color: #121212;

        mat-icon {
          color: #121212;
        }
      }

      mat-card-title {
        color: #ffffff;
      }

      mat-card-subtitle {
        color: #bdbdbd;
      }
    }

    mat-card-actions {
      button {
        &:hover {
          box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
        }
      }
    }
  }

  .remotes-container {
    color: #e0e0e0;
  }

  .header-card {
    mat-card-title {
      color: #ffffff;
    }

    mat-card-subtitle {
      color: #bdbdbd;
    }
  }

  .remotes-list {
    mat-card-title {
      color: #ffffff;
    }

    mat-card-subtitle {
      color: #bdbdbd;
    }
  }

  .no-remotes-card {
    mat-card-header {
      mat-card-title {
        color: #ffffff;
      }

      mat-card-subtitle {
        color: #bdbdbd;
      }

      mat-icon {
        color: #757575;
      }
    }

    mat-card-content p {
      color: #e0e0e0;
    }
  }

  .remote-item {
    color: #e0e0e0;
    border-bottom-color: #424242;

    div[matListItemTitle] {
      color: #ffffff;
    }

    div[matListItemLine] {
      color: #bdbdbd;
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .remotes-container {
    padding: 16px;
    gap: 16px;
  }
}

input {
  @apply w-full;
}
